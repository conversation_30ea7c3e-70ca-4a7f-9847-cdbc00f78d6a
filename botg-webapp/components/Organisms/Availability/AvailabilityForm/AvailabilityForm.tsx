'use client';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useCategory } from '@/lib/hooks/queries/category';
import { TSelectOption } from '@/components/Molecules/RHFItems/RHFSelect/RHFSelect';
import RHFItem from '@/components/Molecules/RHFItems/RHFItems';
import Button from '@/components/Atoms/Button';
import { StyledContainer, StyledFormContainer } from './AvailabilityForm.styled';
import { TAvailabilityFormProps, TFormValues } from './AvailabilityForm.types';
import { TFieldOptions, TKeyOfForm, fieldOptions } from './fieldOptions';
import { TCategory } from '@/lib/types/entities/category';

const transformCategoryData = (categories: any[]): any[] =>
  categories.map(category => {
    const newCategory = { ...category };
    if (newCategory.items && !newCategory.children) {
      newCategory.children = newCategory.items;
    }
    if (newCategory.children) {
      newCategory.children = transformCategoryData(newCategory.children);
    }
    return newCategory;
  });

export const AvailabilityForm: React.FC<TAvailabilityFormProps> = ({ onSubmit }) => {
  const initValues: TFormValues = {
    startDate: dayjs().toString(),
    startTime: dayjs().toString(),
    endTime: dayjs().add(8, 'hours').toString(),
    services: [],
  };

  const methods = useForm<TFormValues>({
    defaultValues: initValues,
  });
  const { data: serviceCategoryData } = useCategory({
    filterValue: ['SERVICE'],
    hasItems: true,
  });
  const servicesData = serviceCategoryData?.[0]?.children || [];
  const transformedServicesData = useMemo(() => transformCategoryData(servicesData), [servicesData]);

  const OPTIONS_BY_FIELDNAME: Partial<Record<TKeyOfForm, TSelectOption[] | undefined>> = useMemo(
    () => ({
      services: transformedServicesData as TSelectOption[],
    }),
    [transformedServicesData]
  );
  return (
    <StyledContainer>
      <FormProvider {...methods}>
        <Box flex="1">
          <form id="AvailabilityForm" onSubmit={methods.handleSubmit(onSubmit)}>
            <StyledFormContainer>
              {(Object.keys(fieldOptions) as Array<keyof TFieldOptions>).map(fieldName => {
                const { type, attributes, containerProps } = fieldOptions[fieldName];
                return (
                  <RHFItem
                    key={fieldName}
                    type={type}
                    attributes={{
                      ...(attributes as any),
                      ...(OPTIONS_BY_FIELDNAME[fieldName] ? { options: OPTIONS_BY_FIELDNAME[fieldName] } : {}),
                    }}
                    containerProps={containerProps}
                  />
                );
              })}
            </StyledFormContainer>
          </form>
        </Box>
        <Stack width="156px" height="100%" justifyContent="flex-start" paddingTop="24px" paddingBottom="24px">
          <Button
            form="AvailabilityForm"
            type="submit"
            color="primary"
            label="Generate"
            variant="contained"
            fullWidth={false}
          />
        </Stack>
      </FormProvider>
    </StyledContainer>
  );
};
